/* POSInterface Component Styles - Light Theme */

.pos-loading-container {
  min-height: 80vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pos-loading-text {
  color: #475569;
  font-size: 20px;
}

.pos-container {
  min-height: 80vh;
  background: #ffffff;
}

.pos-notification {
  position: fixed;
  top: 16px;
  right: 16px;
  background: #16a34a;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 50;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Header Styles */
.pos-header {
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px;
}

.pos-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pos-header-left {
  display: flex;
  align-items: center;
  gap: 1px;
}

.pos-brand {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pos-brand-icon {
  width: 32px;
  height: 32px;
  background: #3b82f6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pos-brand-icon-text {
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.pos-brand-text {
  color: #3b82f6;
  font-weight: bold;
  font-size: 18px;
}

.pos-user-info {
  color: #475569;
  font-size: 14px;
}

.pos-user-name {
  color: #1e40af;
}

.pos-header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pos-sync-button {
  background: #3b82f6;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.pos-sync-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.pos-sync-button-loading {
  background: #93c5fd;
  cursor: not-allowed;
}

.pos-view-toggle-button {
  background: #8b5cf6;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.pos-view-toggle-button:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.4);
}

.pos-sync-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-bottom: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main Content */
.pos-main {
  display: flex;
  height: calc(100vh - 80px);
  background: #ffffff;
  flex-direction: column;
  overflow: hidden;
}

@media (min-width: 900px) {
  .pos-main {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }
}

.pos-products-section {
  flex: 0.9;
  width: 150%;
  padding: 7px;
  overflow-y: hidden;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.pos-cart-section {
  width: 50%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  height: 20vh;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* Category Navigation */
.pos-categories {
  margin-bottom: 24px;
}

.pos-categories-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.pos-category-button {
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: 14px;
  border: 1px solid #e2e8f0;
}

.pos-category-button-active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.pos-category-button-inactive {
  background: #ffffff;
  color: #475569;
  border-color: #e2e8f0;
}

.pos-category-button-inactive:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Products Grid */
.pos-products-empty {
  text-align: center;
  color: #94a3b8;
  padding: 48px 0;
}

.pos-products-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #cbd5e1;
}

.pos-products-empty-title {
  font-size: 20px;
  margin-bottom: 16px;
  color: #475569;
}

.pos-products-empty-description {
  font-size: 14px;
  margin-bottom: 16px;
  color: #94a3b8;
}

.pos-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  padding: 16px 0;
}

.pos-product-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  text-align: center;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pos-product-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  border-color: #3b82f6;
}

.pos-product-card:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pos-product-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
  line-height: 1.2;
  text-align: center;
}

.pos-product-price {
  color: #16a34a;
  font-weight: 600;
  font-size: 12px;
  margin-top: 4px;
}

.pos-product-stock {
  color: #6b7280;
  font-size: 11px;
  margin-top: 2px;
}

/* Cart Styles */
.pos-cart-header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.pos-cart-title {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.pos-cart-items-count {
  color: #6b7280;
  font-size: 14px;
}

.pos-cart-content {
  flex: 1;
  padding: 16px;
  background: #ffffff;
}

.pos-cart-empty {
  text-align: center;
  color: #94a3b8;
  padding: 32px 0;
}

.pos-cart-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #cbd5e1;
}

.pos-cart-empty-text {
  font-size: 16px;
  color: #6b7280;
}

.pos-cart-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pos-cart-item {
  background: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.pos-cart-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.pos-cart-item-name {
  color: #1f2937;
  font-weight: 500;
  font-size: 14px;
  flex: 1;
}

.pos-cart-item-remove {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.pos-cart-item-remove:hover {
  background: #dc2626;
}

.pos-cart-item-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pos-cart-item-quantity {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pos-quantity-button {
  background: #e2e8f0;
  color: #475569;
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.pos-quantity-button:hover {
  background: #cbd5e1;
}

.pos-quantity-display {
  color: #1f2937;
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.pos-cart-item-price {
  color: #16a34a;
  font-weight: bold;
  font-size: 14px;
}

/* Cart Footer */
.pos-cart-footer {
  border-top: 1px solid #e2e8f0;
  padding: 16px;
  background: #f8fafc;
  order: 3;
}

.pos-cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.pos-cart-total-label {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.pos-cart-total-amount {
  color: #16a34a;
  font-size: 24px;
  font-weight: bold;
}

.pos-checkout-button {
  width: 100%;
  background: #16a34a;
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(22, 163, 74, 0.3);
}

.pos-checkout-button:hover {
  background: #15803d;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(22, 163, 74, 0.4);
}

.pos-checkout-button:disabled {
  background: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pos-checkout-button-loading {
  background: #059669;
  cursor: not-allowed;
}

.pos-checkout-button-loading:hover {
  background: #059669;
  transform: none;
  box-shadow: none;
}

/* Function Buttons */
.pos-function-buttons {
  padding: 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  order: 2;
}

.pos-function-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.pos-function-button {
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pos-function-button-primary {
  background: #3b82f6;
  color: white;
}

.pos-function-button-primary:hover {
  background: #2563eb;
}

.pos-function-button-secondary {
  background: #e2e8f0;
  color: #475569;
}

.pos-function-button-secondary:hover {
  background: #cbd5e1;
}

.pos-function-button-warning {
  background: #f59e0b;
  color: white;
}

.pos-function-button-warning:hover {
  background: #d97706;
}

.pos-function-button-danger {
  background: #ef4444;
  color: white;
}

.pos-function-button-danger:hover {
  background: #dc2626;
}

/* Bottom Functions */
.pos-bottom-functions {
  padding: 16px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.pos-logout-button {
  width: 100%;
  background: #ef4444;
  color: white;
  padding: 8px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.pos-logout-button:hover {
  background: #dc2626;
}

.pos-info-display {
  text-align: center;
  margin-top: 12px;
}

.pos-info-text {
  color: #6b7280;
  font-size: 12px;
  margin: 4px 0;
}

/* Responsive Design */
@media (min-width: 1200px) {
  .pos-main {
    flex-direction: row; /* cart left, products right */
  }

  .pos-products-section {
    border-right: 1px solid #e2e8f0;
    border-left: none;
  }

  .pos-cart-section {
    width: 300px;
    min-width: 300px;
    max-width: 300px;
  }

  .pos-products-grid-container {
    overflow-y: auto;
  }
} 
