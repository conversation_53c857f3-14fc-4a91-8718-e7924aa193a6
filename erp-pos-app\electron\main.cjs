const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { initializeDatabase } = require('../db/sqlite.cjs');
const { login, testERPConnection, syncInventoryFromERP, syncSalesToERP } = require('./syncService.cjs');
const config = require('./config.cjs');

// IPC handlers for ERP sync
ipcMain.handle('test-erp-connection', async () => {
  return await testERPConnection();
});

ipcMain.handle('sync-inventory', async () => {
  return await syncInventoryFromERP();
});

ipcMain.handle('sync-sales', async () => {
  return await syncSalesToERP();
});

ipcMain.handle('login', async (event, username, password) => {
  return await login(username, password);
});

function createWindow() {
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  win.loadURL(
    process.env.VITE_DEV_SERVER_URL || `file://${path.join(__dirname, '../dist/index.html')}`
  );
  
  // Open DevTools in development mode
  if (process.env.VITE_DEV_SERVER_URL) {
    win.webContents.openDevTools();
  }
}

app.whenReady().then(() => {
  initializeDatabase();
  createWindow();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
  
  // Setup periodic sync
  setInterval(async () => {
    try {
      await syncSalesToERP();
      console.log('Periodic sales sync completed');
    } catch (err) {
      console.error('Periodic sales sync failed:', err);
    }
  }, config.database.syncInterval);
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});
