/**
 * Data Synchronization Service
 * Handles local vs cloud data synchronization for offline-first POS system
 */

import { config, apiEndpoints } from '../config/environment';

interface SyncStatus {
  isOnline: boolean;
  lastSync: string | null;
  pendingSales: number;
  pendingReports: number;
  syncInProgress: boolean;
}

interface Product {
  id: number;
  name: string;
  price: number;
  barcode?: string;
  category: string;
  stock: number;
  image?: string;
  lastUpdated: string;
}

interface Sale {
  id: string;
  items: any[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  paymentMethod: 'cash' | 'card' | 'digital';
  timestamp: string;
  cashier: string;
  receiptNumber: string;
  synced: boolean;
}

class DataSyncService {
  private authToken: string | null;
  private syncInterval: NodeJS.Timeout | null = null;
  private isOnline: boolean = navigator.onLine;
  private syncInProgress: boolean = false;

  constructor() {
    this.authToken = localStorage.getItem('auth_token');
    
    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
    
    // Start periodic sync when online
    if (this.isOnline) {
      this.startPeriodicSync();
    }
  }

  /**
   * Set authentication token for API requests
   */
  setAuthToken(token: string): void {
    this.authToken = token;
    localStorage.setItem('auth_token', token);
  }

  /**
   * Get current sync status
   */
  getSyncStatus(): SyncStatus {
    const pendingSales = this.getPendingSalesCount();
    const pendingReports = this.getPendingReportsCount();
    const lastSync = localStorage.getItem('last_sync_timestamp');

    return {
      isOnline: this.isOnline,
      lastSync,
      pendingSales,
      pendingReports,
      syncInProgress: this.syncInProgress
    };
  }

  /**
   * Sync products from ERP to local storage
   * Products are always synced FROM ERP TO local (one-way)
   */
  async syncProductsFromERP(): Promise<{ success: boolean; message: string; products?: Product[] }> {
    if (!this.isOnline) {
      return {
        success: false,
        message: 'Cannot sync products while offline. Using cached products.'
      };
    }

    try {
      const response = await fetch(apiEndpoints.pos.productsSync, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success && data.products) {
        // Store products locally with timestamp
        const productsWithTimestamp = data.products.map((product: Product) => ({
          ...product,
          lastUpdated: new Date().toISOString()
        }));

        localStorage.setItem('local_products', JSON.stringify(productsWithTimestamp));
        localStorage.setItem('products_last_sync', new Date().toISOString());

        return {
          success: true,
          message: `Synced ${data.products.length} products from ERP`,
          products: productsWithTimestamp
        };
      }

      return {
        success: false,
        message: 'No products received from ERP'
      };

    } catch (error) {
      console.error('Product sync failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Product sync failed'
      };
    }
  }

  /**
   * Get products from local storage (offline-first)
   */
  getLocalProducts(): Product[] {
    try {
      const products = localStorage.getItem('local_products');
      return products ? JSON.parse(products) : [];
    } catch (error) {
      console.error('Error loading local products:', error);
      return [];
    }
  }

  /**
   * Sync sales TO ERP (local to cloud)
   * Sales are stored locally first, then synced to ERP
   */
  async syncSalesToERP(): Promise<{ success: boolean; message: string; syncedCount?: number }> {
    if (!this.isOnline) {
      return {
        success: false,
        message: 'Cannot sync sales while offline. Sales stored locally.'
      };
    }

    const unsyncedSales = this.getUnsyncedSales();
    
    if (unsyncedSales.length === 0) {
      return {
        success: true,
        message: 'No sales to sync',
        syncedCount: 0
      };
    }

    this.syncInProgress = true;
    let syncedCount = 0;

    try {
      for (const sale of unsyncedSales) {
        try {
          const response = await fetch(apiEndpoints.pos.sales, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(sale)
          });

          if (response.ok) {
            // Mark sale as synced
            this.markSaleAsSynced(sale.id);
            syncedCount++;
          } else {
            console.warn(`Failed to sync sale ${sale.id}:`, response.statusText);
          }
        } catch (error) {
          console.warn(`Error syncing sale ${sale.id}:`, error);
        }
      }

      if (syncedCount > 0) {
        localStorage.setItem('last_sync_timestamp', new Date().toISOString());
      }

      return {
        success: true,
        message: `Synced ${syncedCount} of ${unsyncedSales.length} sales to ERP`,
        syncedCount
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Sales sync failed'
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Store sale locally (offline-first approach)
   */
  storeSaleLocally(sale: Omit<Sale, 'synced'>): void {
    try {
      const localSales = this.getLocalSales();
      const saleWithSyncStatus: Sale = {
        ...sale,
        synced: false
      };
      
      localSales.push(saleWithSyncStatus);
      localStorage.setItem('local_sales', JSON.stringify(localSales));

      // Try to sync immediately if online
      if (this.isOnline) {
        this.syncSalesToERP().catch(console.error);
      }
    } catch (error) {
      console.error('Error storing sale locally:', error);
      throw new Error('Failed to store sale locally');
    }
  }

  /**
   * Get all local sales
   */
  getLocalSales(): Sale[] {
    try {
      const sales = localStorage.getItem('local_sales');
      return sales ? JSON.parse(sales) : [];
    } catch (error) {
      console.error('Error loading local sales:', error);
      return [];
    }
  }

  /**
   * Get unsynced sales
   */
  private getUnsyncedSales(): Sale[] {
    return this.getLocalSales().filter(sale => !sale.synced);
  }

  /**
   * Mark sale as synced
   */
  private markSaleAsSynced(saleId: string): void {
    try {
      const localSales = this.getLocalSales();
      const updatedSales = localSales.map(sale => 
        sale.id === saleId ? { ...sale, synced: true } : sale
      );
      localStorage.setItem('local_sales', JSON.stringify(updatedSales));
    } catch (error) {
      console.error('Error marking sale as synced:', error);
    }
  }

  /**
   * Get count of pending sales
   */
  private getPendingSalesCount(): number {
    return this.getUnsyncedSales().length;
  }

  /**
   * Get count of pending reports
   */
  private getPendingReportsCount(): number {
    try {
      const reports = localStorage.getItem('end_of_day_reports');
      if (!reports) return 0;
      
      const parsedReports = JSON.parse(reports);
      // Count reports that haven't been synced (you can add a synced flag to reports too)
      return parsedReports.length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Full synchronization (products from ERP, sales to ERP)
   */
  async performFullSync(): Promise<{ success: boolean; message: string; details: any }> {
    if (!this.isOnline) {
      return {
        success: false,
        message: 'Cannot perform full sync while offline',
        details: null
      };
    }

    this.syncInProgress = true;

    try {
      // 1. Sync products from ERP
      const productSync = await this.syncProductsFromERP();
      
      // 2. Sync sales to ERP
      const salesSync = await this.syncSalesToERP();

      // 3. Sync end-of-day reports (if any)
      const reportsSync = await this.syncReportsToERP();

      const allSuccessful = productSync.success && salesSync.success && reportsSync.success;

      return {
        success: allSuccessful,
        message: allSuccessful ? 'Full synchronization completed' : 'Partial synchronization completed',
        details: {
          products: productSync,
          sales: salesSync,
          reports: reportsSync
        }
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Full sync failed',
        details: null
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync end-of-day reports to ERP
   */
  private async syncReportsToERP(): Promise<{ success: boolean; message: string }> {
    try {
      const reports = localStorage.getItem('end_of_day_reports');
      if (!reports) {
        return { success: true, message: 'No reports to sync' };
      }

      const parsedReports = JSON.parse(reports);
      let syncedCount = 0;

      for (const report of parsedReports) {
        try {
          const response = await fetch(apiEndpoints.pos.endOfDay, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(report)
          });

          if (response.ok) {
            syncedCount++;
          }
        } catch (error) {
          console.warn('Failed to sync report:', error);
        }
      }

      return {
        success: true,
        message: `Synced ${syncedCount} reports to ERP`
      };

    } catch (error) {
      return {
        success: false,
        message: 'Failed to sync reports'
      };
    }
  }

  /**
   * Handle online event
   */
  private handleOnline(): void {
    this.isOnline = true;
    console.log('Device is online - starting sync');
    
    // Perform full sync when coming back online
    this.performFullSync().then(result => {
      console.log('Auto-sync result:', result);
    });

    // Start periodic sync
    this.startPeriodicSync();
  }

  /**
   * Handle offline event
   */
  private handleOffline(): void {
    this.isOnline = false;
    console.log('Device is offline - stopping sync');
    
    // Stop periodic sync
    this.stopPeriodicSync();
  }

  /**
   * Start periodic synchronization
   */
  private startPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    // Sync at configured interval when online
    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.performFullSync().catch(console.error);
      }
    }, config.features.syncInterval);
  }

  /**
   * Stop periodic synchronization
   */
  private stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Manual sync trigger
   */
  async manualSync(): Promise<{ success: boolean; message: string; details?: any }> {
    if (this.syncInProgress) {
      return {
        success: false,
        message: 'Sync already in progress'
      };
    }

    return await this.performFullSync();
  }

  /**
   * Get last sync information
   */
  getLastSyncInfo(): { timestamp: string | null; productsCount: number; salesCount: number } {
    const timestamp = localStorage.getItem('last_sync_timestamp');
    const products = this.getLocalProducts();
    const sales = this.getLocalSales();

    return {
      timestamp,
      productsCount: products.length,
      salesCount: sales.length
    };
  }

  /**
   * Clear all local data (use with caution)
   */
  clearLocalData(): void {
    localStorage.removeItem('local_products');
    localStorage.removeItem('local_sales');
    localStorage.removeItem('end_of_day_reports');
    localStorage.removeItem('last_sync_timestamp');
    localStorage.removeItem('products_last_sync');
  }

  /**
   * Cleanup - stop intervals and remove listeners
   */
  destroy(): void {
    this.stopPeriodicSync();
    window.removeEventListener('online', this.handleOnline.bind(this));
    window.removeEventListener('offline', this.handleOffline.bind(this));
  }
}

// Export singleton instance
export const dataSyncService = new DataSyncService();
export default dataSyncService;
