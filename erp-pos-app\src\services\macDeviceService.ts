/**
 * MAC Device Service - Handles POS device activation with MAC address binding
 */

import { config } from '../config/environment';

export interface MacDeviceInfo {
  fingerprint: string;
  macAddress: string;
  hardwareInfo: {
    platform: string;
    arch: string;
    userAgent: string;
    screenResolution: string;
    timezone: string;
    language: string;
    cpuCores: number;
    memory: string;
  };
}

export interface MacActivationStatus {
  activated: boolean;
  deviceId?: number;
  deviceName?: string;
  companyName?: string;
  branchName?: string;
  activatedAt?: string;
  lastSeen?: string;
  message?: string;
  macAddress?: string;
}

export interface MacActivationResponse {
  success: boolean;
  message: string;
  device_info?: {
    device_id: number;
    device_name: string;
    branch_name: string;
    company_name: string;
    activated_at: string;
    mac_address: string;
  };
  redirect_url?: string;
}

class MacDeviceService {
  private readonly STORAGE_KEY = 'pos_device_fingerprint';
  private readonly MAC_STORAGE_KEY = 'pos_device_mac_address';
  private getApiBaseUrl(): string {
    const stored = localStorage.getItem('erp_server_url');
    return stored || config.apiUrl;
  }
  private deviceFingerprint: string | null = null;
  private macAddress: string | null = null;

  /**
   * Initialize device service with MAC address
   */
  async initialize(): Promise<MacActivationStatus> {
    try {
      // Generate or retrieve device fingerprint
      this.deviceFingerprint = await this.getOrGenerateFingerprint();
      
      // Get MAC address
      this.macAddress = await this.getMacAddress();
      
      console.log('Device initialized with MAC:', this.macAddress);
      
      // Check if device is activated
      const status = await this.checkActivationStatus();
      
      // Store fingerprint and MAC if device is activated
      if (status.activated && this.deviceFingerprint && this.macAddress) {
        localStorage.setItem(this.STORAGE_KEY, this.deviceFingerprint);
        localStorage.setItem(this.MAC_STORAGE_KEY, this.macAddress);
      }
      
      return status;
    } catch (error) {
      console.error('Failed to initialize device service:', error);
      return {
        activated: false,
        message: 'Failed to check device status'
      };
    }
  }

  /**
   * Get MAC address from Electron or fallback to browser
   */
  private async getMacAddress(): Promise<string> {
    // Try to get from localStorage first
    let macAddress = localStorage.getItem(this.MAC_STORAGE_KEY);
    
    if (!macAddress) {
      // Try to get from Electron API
      try {
        if (window.erpAPI && (window.erpAPI as any).getMacAddress) {
          macAddress = await (window.erpAPI as any).getMacAddress();
        } else {
          // Fallback: generate a browser-based identifier
          macAddress = await this.generateBrowserIdentifier();
        }
      } catch (error) {
        console.warn('Failed to get MAC address, using browser identifier:', error);
        macAddress = await this.generateBrowserIdentifier();
      }
    }
    
    return macAddress || await this.generateBrowserIdentifier();
  }

  /**
   * Generate browser-based identifier when MAC address is not available
   */
  private async generateBrowserIdentifier(): Promise<string> {
    const components = [
      navigator.platform,
      navigator.userAgent,
      screen.width.toString(),
      screen.height.toString(),
      Intl.DateTimeFormat().resolvedOptions().timeZone,
      navigator.language,
      Math.random().toString(36).substring(2, 15)
    ];

    const encoder = new TextEncoder();
    const data = encoder.encode(components.join('|'));
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    
    return `BROWSER_${hashHex.substring(0, 16)}`;
  }

  /**
   * Generate unique device fingerprint
   */
  private async generateDeviceFingerprint(): Promise<string> {
    const macAddress = await this.getMacAddress();

    const components = [
      navigator.platform,
      navigator.userAgent,
      screen.width + 'x' + screen.height,
      navigator.hardwareConcurrency?.toString() || '0',
      navigator.language,
      macAddress,
      window.location.hostname,
      new Date().getTimezoneOffset().toString(),
    ];

    // Create hash from components
    const encoder = new TextEncoder();
    const data = encoder.encode(components.join('|'));
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    
    return `POS_${hashHex.substring(0, 28)}`;
  }

  /**
   * Get or generate device fingerprint
   */
  private async getOrGenerateFingerprint(): Promise<string> {
    // Try to get from localStorage first
    let fingerprint = localStorage.getItem(this.STORAGE_KEY);
    
    if (!fingerprint) {
      // Generate new fingerprint
      fingerprint = await this.generateDeviceFingerprint();
    }
    
    return fingerprint;
  }

  /**
   * Get complete device information including MAC address
   */
  async getDeviceInfo(): Promise<MacDeviceInfo> {
    const fingerprint = await this.getOrGenerateFingerprint();
    const macAddress = await this.getMacAddress();
    
    return {
      fingerprint,
      macAddress: macAddress || 'unknown',
      hardwareInfo: {
        platform: navigator.platform,
        arch: (navigator as any).userAgentData?.platform || 'unknown',
        userAgent: navigator.userAgent,
        screenResolution: `${screen.width}x${screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: navigator.language,
        cpuCores: navigator.hardwareConcurrency || 0,
        memory: (navigator as any).deviceMemory ? `${(navigator as any).deviceMemory}GB` : 'unknown'
      }
    };
  }

  /**
   * Check if device is activated with MAC address validation
   */
  async checkActivationStatus(): Promise<MacActivationStatus> {
    if (!this.deviceFingerprint || !this.macAddress) {
      return { activated: false, message: 'No device fingerprint or MAC address' };
    }

    // If the app was previously activated, trust that in development
    const appActivated = localStorage.getItem('app_activated');
    if (config.isDevelopment && appActivated === 'true') {
      const companyDataRaw = localStorage.getItem('company_data');
      const companyData = companyDataRaw ? JSON.parse(companyDataRaw) : null;
      return {
        activated: true,
        deviceName: 'Dev Device',
        companyName: companyData?.name,
        macAddress: this.macAddress,
        message: 'Dev: trusted local activation'
      };
    }

    // Derive company id from multiple sources
    let storedCompanyId = localStorage.getItem('company_id');
    if (!storedCompanyId) {
      try {
        const activationRaw = localStorage.getItem('device_activation');
        if (activationRaw) {
          const act = JSON.parse(activationRaw);
          if (act?.companyId) storedCompanyId = String(act.companyId);
          else if (act?.companyInfo?.id) storedCompanyId = String(act.companyInfo.id);
        }
      } catch(_){}
    }
    if (!storedCompanyId) {
      return { activated: false, message: 'No company selected' };
    }

    // Development fallback: trust local activation data if present
    try {
      if (config.isDevelopment) {
        const devActivationRaw = localStorage.getItem('device_activation');
        if (devActivationRaw) {
          const devActivation = JSON.parse(devActivationRaw);
          const companyInfo = devActivation?.companyInfo || null;
          if (!localStorage.getItem('erp_server_url') && companyInfo?.server_url) {
            localStorage.setItem('erp_server_url', companyInfo.server_url);
          }
          if (!localStorage.getItem('company_data') && companyInfo) {
            localStorage.setItem('company_data', JSON.stringify(companyInfo));
          }
          return {
            activated: true,
            deviceName: 'Dev Device',
            companyName: companyInfo?.name,
            macAddress: this.macAddress,
            message: 'Development fallback activation'
          };
        }
      }
    } catch (_) {
      // ignore fallback errors
    }

    try {
      const apiBase = this.getApiBaseUrl();
      const response = await fetch(`${apiBase}/api/mac-activation/check-activation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          company_id: storedCompanyId,
          mac_address: this.macAddress
        })
      });

      const data = await response.json();

      if (response.ok && data.success && data.activated) {
        return {
          activated: true,
          deviceId: data.device_info?.company_id,
          deviceName: 'MAC Device',
          companyName: data.device_info?.company_name,
          branchName: 'Main Branch',
          lastSeen: data.device_info?.last_seen,
          macAddress: data.device_info?.mac_address,
          message: data.message || 'Device is activated'
        };
      } else {
        // In development, attempt auto-activation using company license key
        if (config.isDevelopment) {
          try {
            // Fetch company to get license key
            const companyResp = await fetch(`${apiBase}/api/companies/${storedCompanyId}`);
            if (companyResp.ok) {
              const companyJson = await companyResp.json();
              const licenseKey = companyJson?.data?.license_key;
              if (licenseKey) {
                const activate = await this.activateDevice(String(storedCompanyId), licenseKey);
                if (activate.success) {
                  // Re-check status
                  return await this.checkActivationStatus();
                }
              }
            }
          } catch (_) {
            // ignore and fall through
          }
        }

        return {
          activated: false,
          message: data.message || 'Device not activated'
        };
      }
    } catch (error) {
      console.error('Error checking activation status:', error);
      return {
        activated: false,
        message: 'Failed to check activation status'
      };
    }
  }

  /**
   * Activate device with company ID and MAC address binding
   */
  async activateDevice(companyId: string, activationKey: string): Promise<MacActivationResponse> {
    try {
      const deviceInfo = await this.getDeviceInfo();

      const response = await fetch(`${this.getApiBaseUrl()}/api/mac-activation/activate-device`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          company_id: companyId,
          activation_key: activationKey,
          device_fingerprint: deviceInfo.fingerprint,
          mac_address: deviceInfo.macAddress,
          hardware_info: deviceInfo.hardwareInfo,
          app_version: '1.0.0'
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Store fingerprint and MAC on successful activation
        this.deviceFingerprint = deviceInfo.fingerprint;
        this.macAddress = deviceInfo.macAddress;
        localStorage.setItem(this.STORAGE_KEY, deviceInfo.fingerprint);
        localStorage.setItem(this.MAC_STORAGE_KEY, deviceInfo.macAddress);
        localStorage.setItem('company_id', companyId);
        localStorage.setItem('company_data', JSON.stringify(data.device_info));
        localStorage.setItem('app_activated', 'true');

        return {
          success: true,
          message: data.message,
          device_info: data.device_info
        };
      } else {
        return {
          success: false,
          message: data.message || 'Activation failed'
        };
      }
    } catch (error) {
      console.error('Error activating device:', error);
      return {
        success: false,
        message: 'Network error during activation'
      };
    }
  }

  /**
   * Get current MAC address
   */
  getMacAddressValue(): string | null {
    return this.macAddress;
  }

  /**
   * Get current device fingerprint
   */
  getFingerprint(): string | null {
    return this.deviceFingerprint;
  }

  /**
   * Clear one-time activation data (MASTER ADMIN ONLY)
   * This should only be used by master administrators to reset a device
   */
  async clearOneTimeActivation(adminPassword: string = ''): Promise<{ success: boolean; message: string }> {
    // Simple password check (in production, this should be more secure)
    const MASTER_ADMIN_PASSWORD = 'RESET_DEVICE_2024';

    if (adminPassword !== MASTER_ADMIN_PASSWORD) {
      return {
        success: false,
        message: 'Invalid master admin password. Only master administrators can reset device activation.'
      };
    }

    try {
      // Clear one-time activation data
      localStorage.removeItem('device_activation');
      localStorage.removeItem('device_fingerprint');

      // Clear related data
      localStorage.removeItem('erp_server_url');
      localStorage.removeItem('company_data');
      localStorage.removeItem('app_activated');
      localStorage.removeItem('logged_in');
      localStorage.removeItem('user_data');
      localStorage.removeItem('auth_token');
      localStorage.removeItem('company_id');
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.MAC_STORAGE_KEY);

      // Clear sales and reports (optional - comment out if you want to keep data)
      // localStorage.removeItem('local_sales');
      // localStorage.removeItem('end_of_day_reports');

      console.log('One-time activation cleared by master admin');

      return {
        success: true,
        message: 'Device activation has been reset. The device can now be reactivated with a new company.'
      };
    } catch (error) {
      console.error('Failed to clear activation:', error);
      return {
        success: false,
        message: 'Failed to clear activation data. Please try again.'
      };
    }
  }

  /**
   * Check if device can be reactivated (for admin interface)
   */
  canReactivate(): boolean {
    const activation = localStorage.getItem('device_activation');
    return !activation; // Can only reactivate if no activation exists
  }
}

// Export singleton instance
export const macDeviceService = new MacDeviceService();
