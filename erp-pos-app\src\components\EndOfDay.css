/* EndOfDay Component Styles */







.end-of-day-container {



  min-height: 100vh;



  background: linear-gradient(135deg, #1f2937 0%, #1e3a8a 50%, #581c87 100%);



}







.end-of-day-header {



  background: rgba(0, 0, 0, 0.2);



  backdrop-filter: blur(16px);



  border-bottom: 1px solid rgba(255, 255, 255, 0.1);



  padding: 16px;



}







.end-of-day-header-content {



<<<<<<<
  display: flex;

=======
.end-of-day-container-inner {

  max-width: 1200px;

  margin: 0 auto;

}



.end-of-day-summary-grid {

  display: grid;

  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));

  gap: 24px;

  margin-bottom: 32px;

}

>>>>>>>


<<<<<<<
  justify-content: space-between;

=======
@media (min-width: 768px) {

  .end-of-day-summary-grid {

    grid-template-columns: repeat(3, 1fr);

  }

}



.end-of-day-summary-card {

  background: rgba(255, 255, 255, 0.1);

  backdrop-filter: blur(16px);

  border-radius: 16px;

  padding: 24px;

  border: 1px solid rgba(255, 255, 255, 0.2);

  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  text-align: center;

  display: flex;

  flex-direction: column;

  align-items: center;

  justify-content: center;

}

>>>>>>>


  align-items: center;



}







.end-of-day-brand {



  display: flex;



  align-items: center;



  gap: 12px;



}







<<<<<<<
.end-of-day-brand-icon {

=======
@media (min-width: 768px) {

  .end-of-day-buttons-grid {

    grid-template-columns: repeat(2, 1fr);

  }

}



.end-of-day-action-button {

  padding: 16px 32px;

  border-radius: 12px;

  font-weight: bold;

  transition: all 0.2s ease;

  border: none;

  cursor: pointer;

  color: white;

  transform: scale(1);

}

>>>>>>>


  width: 32px;



  height: 32px;



  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);



  border-radius: 8px;



  display: flex;



  align-items: center;



<<<<<<<
  justify-content: center;

=======
.end-of-day-low-stock-panel {

  background: rgba(245, 158, 11, 0.2);

  backdrop-filter: blur(16px);

  border-radius: 16px;

  padding: 24px;

  border: 1px solid rgba(245, 158, 11, 0.3);

  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

}

>>>>>>>


}







<<<<<<<
.end-of-day-brand-text {

=======
@media (min-width: 768px) {

  .end-of-day-low-stock-grid {

    grid-template-columns: repeat(2, 1fr);

  }

}



@media (min-width: 1024px) {

  .end-of-day-low-stock-grid {

    grid-template-columns: repeat(3, 1fr);

  }

}



.end-of-day-low-stock-item {

  background: rgba(255, 255, 255, 0.1);

  backdrop-filter: blur(16px);

  border-radius: 12px;

  padding: 16px;

  border: 1px solid rgba(255, 255, 255, 0.2);

  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);

}

>>>>>>>


<<<<<<<
  font-size: 20px;

=======
.end-of-day-low-stock-item-name {

  color: white;

  font-weight: 500;

  font-size: 14px;

  margin-bottom: 8px;

}

>>>>>>>


  font-weight: bold;



  color: white;



}







.end-of-day-user-info {



  color: rgba(255, 255, 255, 0.8);



}







.end-of-day-actions {



  display: flex;



  align-items: center;



  gap: 16px;



}







.end-of-day-button {



  padding: 8px 16px;



  border-radius: 8px;



  font-weight: 500;



  transition: all 0.2s ease;



  border: none;



  cursor: pointer;



  color: white;



}







.end-of-day-button-secondary {



  background: #4b5563;



}







.end-of-day-button-secondary:hover {



  background: #374151;



}







.end-of-day-button-danger {



  background: rgba(239, 68, 68, 0.8);



}







.end-of-day-button-danger:hover {



  background: #ef4444;



}







.end-of-day-content {



  padding: 24px;



}







.end-of-day-summary-grid {



  display: grid;



  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));



  gap: 24px;



  margin-bottom: 32px;



}







.end-of-day-summary-card {



  background: rgba(255, 255, 255, 0.1);



  backdrop-filter: blur(16px);



  border-radius: 16px;



  padding: 24px;



  border: 1px solid rgba(255, 255, 255, 0.2);



  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);



  text-align: center;



}







.end-of-day-summary-title {



  color: rgba(255, 255, 255, 0.7);



  font-size: 12px;



  font-weight: 500;



  text-transform: uppercase;



  letter-spacing: 0.05em;



  margin-bottom: 8px;



}







.end-of-day-summary-value {



  font-size: 30px;



  font-weight: bold;



  color: white;



}







.end-of-day-summary-value-green {



  color: #10b981;



}







.end-of-day-summary-value-blue {



  color: #3b82f6;



}







.end-of-day-actions-panel {



  background: rgba(255, 255, 255, 0.1);



  backdrop-filter: blur(16px);



  border-radius: 16px;



  padding: 32px;



  margin-bottom: 32px;



  border: 1px solid rgba(255, 255, 255, 0.2);



  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);



}







.end-of-day-actions-title {



  font-size: 24px;



  font-weight: bold;



  color: white;



  margin-bottom: 24px;



  text-align: center;



}







.end-of-day-status {



  background: rgba(59, 130, 246, 0.2);



  border: 1px solid rgba(59, 130, 246, 0.3);



  border-radius: 12px;



  padding: 16px;



  margin-bottom: 24px;



}







.end-of-day-status-text {



  color: #bfdbfe;



  text-align: center;



}







.end-of-day-buttons-grid {



  display: grid;



  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));



  gap: 16px;



}







.end-of-day-action-button {



  padding: 16px 32px;



  border-radius: 12px;



  font-weight: bold;



  transition: all 0.2s ease;



  border: none;



  cursor: pointer;



  color: white;



  transform: scale(1);



}







.end-of-day-action-button:hover {



  transform: scale(1.02);



}







.end-of-day-action-button:disabled {



  opacity: 0.5;



  cursor: not-allowed;



  transform: none;



}







.end-of-day-action-button-primary {



  background: linear-gradient(135deg, #16a34a 0%, #10b981 100%);



}







.end-of-day-action-button-primary:hover {



  background: linear-gradient(135deg, #15803d 0%, #059669 100%);



}







.end-of-day-action-button-secondary {



  background: linear-gradient(135deg, #2563eb 0%, #6366f1 100%);



}







.end-of-day-action-button-secondary:hover {



  background: linear-gradient(135deg, #1d4ed8 0%, #4f46e5 100%);



}







.end-of-day-low-stock-alert {



  background: rgba(245, 158, 11, 0.2);



  backdrop-filter: blur(16px);



  border-radius: 16px;



  padding: 24px;



  border: 1px solid rgba(245, 158, 11, 0.3);



  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);



}







.end-of-day-low-stock-title {



  font-size: 20px;



  font-weight: bold;



  color: #fbbf24;



  margin-bottom: 16px;



}







.end-of-day-low-stock-grid {



  display: grid;



  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));



  gap: 16px;



}







.end-of-day-low-stock-item {



  background: rgba(255, 255, 255, 0.1);



  border-radius: 8px;



  padding: 12px;



  border: 1px solid rgba(255, 255, 255, 0.2);



}







.end-of-day-low-stock-item-name {



  color: white;



  font-weight: 500;



  font-size: 14px;



  margin-bottom: 4px;



}







.end-of-day-low-stock-item-quantity {



  color: #fbbf24;



  font-weight: bold;



}







/* Loading States */



.end-of-day-loading {



  min-height: 100vh;



  background: linear-gradient(135deg, #1f2937 0%, #1e3a8a 50%, #581c87 100%);



  display: flex;



  align-items: center;



  justify-content: center;



}







.end-of-day-loading-text {



  color: white;



  font-size: 20px;



}







/* Responsive Design */



@media (max-width: 768px) {



  .end-of-day-header-content {



    flex-direction: column;



    gap: 16px;



    align-items: flex-start;



  }



  



  .end-of-day-content {



    padding: 16px;



  }



  



  .end-of-day-summary-grid {



    grid-template-columns: 1fr;



    gap: 16px;



  }



  



  .end-of-day-actions-panel {



    padding: 24px;



  }



  



  .end-of-day-buttons-grid {



    grid-template-columns: 1fr;



  }



  



  .end-of-day-low-stock-grid {



    grid-template-columns: 1fr;



  }



}



