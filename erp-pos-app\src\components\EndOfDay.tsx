import React, { useState, useEffect } from 'react';
import { canAccess, getRoleInfo } from '../config/roles';
import { dataSyncService } from '../services/dataSyncService';
import './EndOfDay.css';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface EndOfDayData {
  date: string;
  openingCash: number;
  totalSales: number;
  totalTransactions: number;
  cashSales: number;
  cardSales: number;
  digitalSales: number;
  totalDiscounts: number;
  totalTax: number;
  expectedCash: number;
  actualCash: number;
  cashVariance: number;
  sales: Sale[];
  reportGeneratedBy: string;
  reportGeneratedAt: string;
}

interface Sale {
  id: string;
  items: any[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  paymentMethod: 'cash' | 'card' | 'digital';
  cashReceived?: number;
  change?: number;
  timestamp: string;
  cashier: string;
  receiptNumber: string;
}



interface SalesSummary {
  total_sales: number;
  total_amount: number;
  total_items_sold: number;
  average_sale: number;
  payment_methods: Record<string, number>;
  hourly_breakdown: Record<string, { count: number; amount: number }>;
  top_products: any[];
  cashier_performance: any;
}

interface EndOfDayProps {
  user: User;
  token: string;
  onBack: () => void;
  onLogout: () => void;
}

const EndOfDay: React.FC<EndOfDayProps> = ({ user, token, onBack, onLogout }) => {
  // Role-based access check
  getRoleInfo(user.role); // Check role permissions
  const [_currentStep, _setCurrentStep] = useState<'verify' | 'count' | 'report' | 'complete'>('verify');

  // State for Z-report functionality
  const [, setTodaysSales] = useState<Sale[]>([]);
  const [salesSummary, setSalesSummary] = useState<SalesSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState('');
  const [stockData] = useState<any[]>([]);
  const [isGeneratingReport] = useState(false);
  const [_isGeneratingReport, _setIsGeneratingReport] = useState(false);
  const [_openingCash, _setOpeningCash] = useState<number>(0);
  const [_actualCashCount, _setActualCashCount] = useState<number>(0);
  const [_reportData, _setReportData] = useState<EndOfDayData | null>(null);

  // Cash breakdown for detailed counting
  const [_cashBreakdown, _setCashBreakdown] = useState({
    bills_100: 0, bills_50: 0, bills_20: 0, bills_10: 0, bills_5: 0, bills_1: 0,
    coins_1: 0, coins_050: 0, coins_025: 0, coins_010: 0, coins_005: 0, coins_001: 0
  });

  // Check permissions on mount
  useEffect(() => {
    if (!canAccess.endOfDay(user.role)) {
      alert('You do not have permission to access end-of-day reports');
      onBack();
      return;
    }

    loadTodaysSales();
  }, [user.role, onBack]);

  const loadTodaysSales = async () => {
    try {
      setIsLoading(true);

      // Load sales from local storage (offline-first)
      const localSales = dataSyncService.getLocalSales();
      const today = new Date().toISOString().split('T')[0];

      const todaySales = localSales.filter((sale: any) =>
        sale.timestamp.startsWith(today)
      );

      setTodaysSales(todaySales);

      // Calculate summary from local sales
      const summary = calculateSalesSummary(todaySales);
      setSalesSummary(summary);

      // Load opening cash from storage
      const storedOpeningCash = localStorage.getItem(`opening_cash_${today}`);
      if (storedOpeningCash) {
        _setOpeningCash(Number(storedOpeningCash));
      }

      console.log('Loaded today\'s sales:', todaySales.length);

    } catch (error) {
      console.error('Failed to load today\'s sales:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateSalesSummary = (sales: any[]): SalesSummary => {
    const totalSales = sales.length;
    const totalAmount = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalItemsSold = sales.reduce((sum, sale) =>
      sum + sale.items.reduce((itemSum: number, item: any) => itemSum + item.quantity, 0), 0
    );
    const averageSale = totalSales > 0 ? totalAmount / totalSales : 0;

    // Payment methods breakdown
    const paymentMethods: Record<string, number> = {};
    sales.forEach(sale => {
      paymentMethods[sale.paymentMethod] = (paymentMethods[sale.paymentMethod] || 0) + sale.total;
    });

    return {
      total_sales: totalSales,
      total_amount: totalAmount,
      total_items_sold: totalItemsSold,
      average_sale: averageSale,
      payment_methods: paymentMethods,
      hourly_breakdown: {},
      top_products: [],
      cashier_performance: null
    };
  };

  // Enhanced Z-report functionality available for future implementation

  const generateReport = async () => {
    _setIsGeneratingReport(true);

    try {
      const serverUrl = localStorage.getItem('erp_server_url');
      const today = new Date().toISOString().split('T')[0];

      const response = await fetch(`${serverUrl}/api/end-of-day/report?date=${today}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const report = data.data;

          const reportContent = `
COMPREHENSIVE DAILY SALES REPORT
================================
Date: ${report.report_date}
Generated: ${new Date(report.generated_at).toLocaleString()}
Generated by: ${report.generated_by}

SALES SUMMARY
-------------
Total Transactions: ${report.sales_summary.total_sales}
Total Amount: $${report.sales_summary.total_amount.toFixed(2)}
Total Items Sold: ${report.sales_summary.total_items_sold}
Average Transaction: $${report.sales_summary.average_sale.toFixed(2)}

PAYMENT METHODS
---------------
${Object.entries(report.sales_summary.payment_methods || {}).map(([method, amount]) =>
  `${method.toUpperCase()}: $${(amount as number).toFixed(2)}`
).join('\n')}

HOURLY BREAKDOWN
----------------
${Object.entries(report.sales_summary.hourly_breakdown || {}).map(([hour, data]) =>
  `${hour}: ${(data as any).count} sales, $${(data as any).amount.toFixed(2)}`
).join('\n')}

TOP PRODUCTS
------------
${report.sales_summary.top_products.slice(0, 10).map((item: any, index: number) =>
  `${index + 1}. ${item.product.name}: ${item.quantity_sold} sold, $${item.total_revenue.toFixed(2)}`
).join('\n')}

LOW STOCK ALERTS
----------------
${report.low_stock_items.map((item: any) =>
  `${item.name} (SKU: ${item.sku}): ${item.stock_quantity} remaining`
).join('\n')}

RECOMMENDATIONS
---------------
${report.recommendations.map((rec: any) =>
  `[${rec.priority.toUpperCase()}] ${rec.message}`
).join('\n')}

Generated on: ${new Date().toLocaleString()}
          `;

          // Create and download report
          const blob = new Blob([reportContent], { type: 'text/plain' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `comprehensive-daily-report-${today}.txt`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);

          setSyncStatus('📄 Comprehensive report generated and downloaded!');
        }
      }
    } catch (error) {
      console.error('Failed to generate report:', error);
      setSyncStatus('❌ Failed to generate report. Please try again.');
    } finally {
      _setIsGeneratingReport(false);
    }
  };

  const syncWithERP = async () => {
    setIsSyncing(true);
    setSyncStatus('🔄 Syncing with ERP...');

    try {
      const syncResult = await dataSyncService.manualSync();
      if (syncResult.success) {
        setSyncStatus('✅ Successfully synced with ERP');
      } else {
        setSyncStatus(`⚠️ ${syncResult.message}`);
      }
    } catch (error) {
      setSyncStatus('❌ Sync failed. Please check your connection.');
    } finally {
      setIsSyncing(false);
      setTimeout(() => setSyncStatus(''), 5000);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading end of day data...</div>
      </div>
    );
  }

  return (
    <div >
      {/* Header */}
      <header >
        <div >
          <div>
            <div >
              <span></span>
            </div>
            <h1 >End of Day</h1>
          </div>
          <div >
            <span >Cashier: {user.name}</span>
            <button
              onClick={onBack}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
            >
              Back to POS
            </button>
            <button
              onClick={onLogout}
              className="bg-red-500/80 hover:bg-red-500 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      <div className="p-6">
        <div className="max-w-6xl mx-auto">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl">
              <div className="text-center">
                <h3 className="text-white/70 text-sm font-medium uppercase tracking-wider mb-2">
                  Total Sales
                </h3>
                <p className="text-3xl font-bold text-white">
                  {salesSummary?.total_sales || 0}
                </p>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl">
              <div className="text-center">
                <h3 className="text-white/70 text-sm font-medium uppercase tracking-wider mb-2">
                  Total Amount
                </h3>
                <p className="text-3xl font-bold text-emerald-300">
                  ${salesSummary?.total_amount.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl">
              <div className="text-center">
                <h3 className="text-white/70 text-sm font-medium uppercase tracking-wider mb-2">
                  Average Sale
                </h3>
                <p className="text-3xl font-bold text-blue-300">
                  ${salesSummary?.average_sale.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8 border border-white/20 shadow-xl">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">End of Day Actions</h2>
            
            {syncStatus && (
              <div className="bg-blue-500/20 border border-blue-500/30 rounded-xl p-4 mb-6">
                <p className="text-blue-200 text-center">{syncStatus}</p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={syncWithERP}
                disabled={isSyncing}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-4 rounded-xl font-bold transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isSyncing ? 'Syncing with ERP...' : 'Sync with ERP'}
              </button>
              
              <button
                onClick={generateReport}
                disabled={isGeneratingReport}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-4 rounded-xl font-bold transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isGeneratingReport ? 'Generating Report...' : 'Generate Comprehensive Report'}
              </button>
            </div>
          </div>

          {/* Low Stock Alert */}
          {stockData.filter(item => item.stock_quantity < 10).length > 0 && (
            <div className="bg-amber-500/20 backdrop-blur-lg rounded-2xl p-6 border border-amber-500/30 shadow-xl">
              <h3 className="text-xl font-bold text-amber-200 mb-4">Low Stock Alert</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {stockData
                  .filter(item => item.stock_quantity < 10)
                  .map((item, index) => (
                    <div key={index} className="bg-white/10 rounded-lg p-3 border border-white/20">
                      <h4 className="text-white font-medium text-sm">{item.name}</h4>
                      <p className="text-amber-300 font-bold">Stock: {item.stock_quantity}</p>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EndOfDay;
