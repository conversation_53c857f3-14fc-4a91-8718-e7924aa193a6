/**
 * Environment Configuration
 * Handles environment variables and app configuration
 */

interface AppConfig {
  apiUrl: string;
  apiBasePath: string;
  appName: string;
  appVersion: string;
  isDevelopment: boolean;
  isProduction: boolean;
  debugMode: boolean;
  features: {
    offlineMode: boolean;
    syncEnabled: boolean;
    syncInterval: number;
    macBinding: boolean;
  };
  security: {
    sessionTimeout: number;
  };
}

/**
 * Get environment variable with fallback
 */
const getEnvVar = (key: string, fallback: string = ''): string => {
  // Check if we're in a Vite environment
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[key] || fallback;
  }
  
  // Fallback for other environments
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key] || fallback;
  }
  
  // Final fallback
  return fallback;
};

/**
 * Get boolean environment variable
 */
const getBooleanEnvVar = (key: string, fallback: boolean = false): boolean => {
  const value = getEnvVar(key, fallback.toString());
  return value.toLowerCase() === 'true';
};

/**
 * Get number environment variable
 */
const getNumberEnvVar = (key: string, fallback: number = 0): number => {
  const value = getEnvVar(key, fallback.toString());
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
};

/**
 * Application configuration
 */
export const config: AppConfig = {
  // API Configuration
  apiUrl: getEnvVar('VITE_API_URL', 'http://127.0.0.1:8000'),
  apiBasePath: getEnvVar('VITE_API_BASE_PATH', '/api'),
  
  // App Information
  appName: getEnvVar('VITE_APP_NAME', 'ERP POS System'),
  appVersion: getEnvVar('VITE_APP_VERSION', '1.0.0'),
  
  // Environment
  isDevelopment: getEnvVar('NODE_ENV', 'development') === 'development',
  isProduction: getEnvVar('NODE_ENV', 'development') === 'production',
  debugMode: getBooleanEnvVar('VITE_DEBUG_MODE', true),
  
  // Features
  features: {
    offlineMode: getBooleanEnvVar('VITE_ENABLE_OFFLINE_MODE', true),
    syncEnabled: getBooleanEnvVar('VITE_ENABLE_SYNC', true),
    syncInterval: getNumberEnvVar('VITE_SYNC_INTERVAL', 300000), // 5 minutes
    macBinding: getBooleanEnvVar('VITE_ENABLE_MAC_BINDING', true),
  },
  
  // Security
  security: {
    sessionTimeout: getNumberEnvVar('VITE_SESSION_TIMEOUT', 3600000), // 1 hour
  }
};

/**
 * API endpoints configuration
 */
export const apiEndpoints = {
  // Base URLs
  base: config.apiUrl,
  api: `${config.apiUrl}${config.apiBasePath}`,
  
  // Authentication
  auth: {
    login: `${config.apiUrl}${config.apiBasePath}/auth/login`,
    logout: `${config.apiUrl}${config.apiBasePath}/auth/logout`,
    refresh: `${config.apiUrl}${config.apiBasePath}/auth/refresh`,
    verify: `${config.apiUrl}${config.apiBasePath}/auth/verify`,
  },
  
  // POS Endpoints
  pos: {
    activate: `${config.apiUrl}${config.apiBasePath}/pos/activate`,
    status: `${config.apiUrl}${config.apiBasePath}/pos/device/status`,
    verifyActivation: `${config.apiUrl}${config.apiBasePath}/pos/verify-activation`,
    products: `${config.apiUrl}${config.apiBasePath}/products`,
    productsSync: `${config.apiUrl}${config.apiBasePath}/sync/products`,
    sales: `${config.apiUrl}${config.apiBasePath}/sales`,
    endOfDay: `${config.apiUrl}${config.apiBasePath}/end-of-day`,
    users: `${config.apiUrl}${config.apiBasePath}/users`,
  },
  
  // Company Endpoints
  company: {
    info: `${config.apiUrl}${config.apiBasePath}/company/info`,
    validate: `${config.apiUrl}${config.apiBasePath}/company/validate`,
  }
};

/**
 * Development helpers
 */
export const isDev = config.isDevelopment;
export const isProd = config.isProduction;
export const isDebug = config.debugMode;

/**
 * Log configuration in development
 */
if (isDev && isDebug) {
  console.group('🔧 App Configuration');
  console.log('Environment:', config.isDevelopment ? 'Development' : 'Production');
  console.log('API URL:', config.apiUrl);
  console.log('Debug Mode:', config.debugMode);
  console.log('Features:', config.features);
  console.groupEnd();
}

/**
 * Validate required configuration
 */
const validateConfig = (): void => {
  const required = [
    { key: 'apiUrl', value: config.apiUrl, name: 'API URL' },
  ];
  
  const missing = required.filter(item => !item.value);
  
  if (missing.length > 0) {
    const missingKeys = missing.map(item => item.name).join(', ');
    throw new Error(`Missing required configuration: ${missingKeys}`);
  }
};

// Validate configuration on import
validateConfig();

export default config;
