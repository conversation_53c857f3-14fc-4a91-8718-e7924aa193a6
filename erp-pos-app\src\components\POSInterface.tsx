/**
 * POS Interface - Traditional POS layout with real product sync from ERP
 */

import React, { useState, useEffect } from 'react';
import type { ActivationStatus } from '../services/deviceService';
import { canAccess, getRoleInfo } from '../config/roles';
import { dataSyncService } from '../services/dataSyncService';
import './POSInterface.css';

interface POSInterfaceProps {
  onLogout: () => void;
  onShowEndOfDay: () => void;
  deviceInfo: ActivationStatus | null;
  user: {
    id: number;
    name: string;
    email: string;
    role: string;
  };
  token: string;
}

interface Product {
  id: number;
  name: string;
  sku: string;
  price: number | string;
  stock_quantity: number | string;
  category?: string;
  group?: any;
}

interface CartItem {
  product: Product;
  quantity: number;
  total: number;
}

interface Sale {
  id: number;
  sale_number: string;
  total_amount: number;
  sale_date: string;
  created_at: string;
  payment_status: string;
  user?: {
    name: string;
  };
  saleItems?: SaleItem[];
  payments?: Payment[];
}

interface SaleItem {
  id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  product: {
    name: string;
    sku: string;
  };
}

interface Payment {
  id: number;
  method: string;
  amount: number;
}

interface SalesSummary {
  total_sales: number;
  total_amount: number;
  total_items_sold: number;
  average_sale: number;
  payment_methods: Record<string, number>;
}

const POSInterface: React.FC<POSInterfaceProps> = ({ 
  onLogout,
  onShowEndOfDay,
  user,
  token
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [products, setProducts] = useState<Product[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('ALL');
  const [currentInput, setCurrentInput] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState('');
  const [viewMode, setViewMode] = useState<'pos' | 'sales'>('pos');
  const [, setTodaysSales] = useState<Sale[]>([]);
  const [olderSales, setOlderSales] = useState<Sale[]>([]);
  const [salesSummary, setSalesSummary] = useState<SalesSummary | null>(null);
  const [isLoadingSales, setIsLoadingSales] = useState(false);
  const [, setSelectedSale] = useState<Sale | null>(null);

  // Role-based state
  getRoleInfo(user.role); // Check user role permissions
  const [globalDiscount, setGlobalDiscount] = useState<number>(0);
  const [paymentMethod, _setPaymentMethod] = useState<'cash' | 'card' | 'digital'>('cash');
  const [cashReceived, setCashReceived] = useState<number>(0);

  // Data sync state
  const [_lastSyncTime, setLastSyncTime] = useState<string | null>(null);
  const [_pendingSales, setPendingSales] = useState<number>(0);

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Load products on component mount
  useEffect(() => {
    loadProducts();
  }, []);

  // Load sales data when switching to sales view
  useEffect(() => {
    if (viewMode === 'sales') {
      loadSalesData();
    }
  }, [viewMode]);

  const loadSalesData = async () => {
    setIsLoadingSales(true);
    try {
      const serverUrl = localStorage.getItem('erp_server_url');
      const today = new Date().toISOString().split('T')[0];
      
      // Load today's sales summary
      const todayResponse = await fetch(`${serverUrl}/api/end-of-day/sales?date=${today}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      if (todayResponse.ok) {
        const data = await todayResponse.json();
        if (data.success && data.data) {
          setTodaysSales(data.data.sales || []);
          setSalesSummary(data.data.summary);
        }
      }

      // Load older sales (last 7 days excluding today)
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      const olderResponse = await fetch(`${serverUrl}/api/sales?date_from=${weekAgo.toISOString().split('T')[0]}&date_to=${today}&per_page=20`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      if (olderResponse.ok) {
        const data = await olderResponse.json();
        if (data.success && data.data) {
          // Filter out today's sales from older sales
          const filteredOlderSales = data.data.data.filter((sale: Sale) => 
            sale.sale_date !== today
          );
          setOlderSales(filteredOlderSales);
        }
      }
    } catch (error) {
      console.error('Failed to load sales data:', error);
    } finally {
      setIsLoadingSales(false);
    }
  };

  // Helper function to safely convert price to number
  const getProductPrice = (product: Product): number => {
    const price = typeof product.price === 'string' ? parseFloat(product.price) : product.price;
    return isNaN(price) ? 0 : price;
  };

  // Helper function to safely convert stock to number
  const getProductStock = (product: Product): number => {
    const stock = typeof product.stock_quantity === 'string' ? parseInt(product.stock_quantity) : product.stock_quantity;
    return isNaN(Number(stock)) ? 0 : Number(stock);
  };

  const loadProducts = async () => {
    try {
      setIsLoading(true);
      setSyncStatus('🔄 Loading products...');

      // First, load products from local storage (offline-first)
      const localProducts = dataSyncService.getLocalProducts();
      if (localProducts.length > 0) {
        const processedProducts = localProducts.map((product: any) => ({
          ...product,
          price: getProductPrice(product),
          stock_quantity: getProductStock(product)
        }));

        setProducts(processedProducts);

        // Extract unique categories from products
        const uniqueCategories: string[] = [...new Set<string>(
          processedProducts
            .map((product: Product) => product.group?.name || product.category || 'UNCATEGORIZED')
            .filter(Boolean)
        )];
        setCategories(['ALL', ...uniqueCategories]);

        console.log('Loaded products from local storage:', processedProducts.length);
        setSyncStatus(`✅ Loaded ${processedProducts.length} products (offline)`);
      }

      // Try to sync with ERP if user has permission and is online
      if (canAccess.syncProducts(user.role)) {
        try {
          const syncResult = await dataSyncService.syncProductsFromERP();
          if (syncResult.success && syncResult.products) {
            const processedProducts = syncResult.products.map((product: any) => ({
              ...product,
              price: getProductPrice(product),
              stock_quantity: getProductStock(product)
            }));

            setProducts(processedProducts);

            // Update categories
            const uniqueCategories: string[] = [...new Set<string>(
              processedProducts
                .map((product: Product) => product.group?.name || product.category || 'UNCATEGORIZED')
                .filter(Boolean)
            )];
            setCategories(['ALL', ...uniqueCategories]);

            setSyncStatus(`✅ Synced ${processedProducts.length} products from ERP`);
          } else {
            setSyncStatus('⚠️ Using cached products (sync failed)');
          }
        } catch (error) {
          console.warn('Product sync failed:', error);
          setSyncStatus('⚠️ Using cached products (offline)');
        }
      }

      // Update sync status and pending sales count
      const syncStatus = dataSyncService.getSyncStatus();
      setLastSyncTime(syncStatus.lastSync);
      setPendingSales(syncStatus.pendingSales);

    } catch (error) {
      console.error('Failed to load products:', error);
      setSyncStatus('❌ Connection error. Check network.');
    } finally {
      setIsLoading(false);
      setTimeout(() => setSyncStatus(''), 5000);
    }
  };

  const syncProducts = async () => {
    // Check if user has permission to sync products
    if (!canAccess.syncProducts(user.role)) {
      setSyncStatus('❌ You do not have permission to sync products');
      setTimeout(() => setSyncStatus(''), 3000);
      return;
    }

    setIsSyncing(true);
    setSyncStatus('🔄 Syncing products from ERP...');

    try {
      const syncResult = await dataSyncService.syncProductsFromERP();

      if (syncResult.success && syncResult.products) {
        const processedProducts = syncResult.products.map((product: any) => ({
          ...product,
          price: getProductPrice(product),
          stock_quantity: getProductStock(product)
        }));

        setProducts(processedProducts);

        // Update categories
        const uniqueCategories: string[] = [...new Set<string>(
          processedProducts
            .map((product: Product) => product.group?.name || product.category || 'UNCATEGORIZED')
            .filter(Boolean)
        )];
        setCategories(['ALL', ...uniqueCategories]);

        setSyncStatus(`✅ Synced ${processedProducts.length} products from ERP`);

        // Update sync status
        const status = dataSyncService.getSyncStatus();
        setLastSyncTime(status.lastSync);
      } else {
        setSyncStatus(`⚠️ ${syncResult.message}`);
      }
      
    } catch (error) {
      console.error('Sync error:', error);
      setSyncStatus('❌ Sync failed. Refreshing local data...');
      // Still try to reload products even if sync failed
      await loadProducts();
    } finally {
      setIsSyncing(false);
      setTimeout(() => setSyncStatus(''), 5000);
    }
  };


  const handleProductClick = (product: Product) => {
    const quantity = currentInput ? parseInt(currentInput) : 1;
    addToCart(product, quantity);
    setCurrentInput('');
  };

    const addToCart = (product: Product, quantity: number = 1) => {
      console.log('Adding to cart:', product, 'Quantity:', quantity); // Debug log
    const price = getProductPrice(product);
    
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.product.id === product.id);
      
      if (existingItem) {
        return prevCart.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + quantity, total: (item.quantity + quantity) * price }
            : item
        );
      } else {
        return [...prevCart, { product, quantity, total: price * quantity }];
      }
    });
  };

  const handleKeypadClick = (value: string) => {
    if (value === 'CLEAR') {
      setCurrentInput('');
    } else if (value === 'REPEAT') {
      // Repeat last item logic - could add last item to cart again
      if (cart.length > 0) {
        const lastItem = cart[cart.length - 1];
        addToCart(lastItem.product, 1);
      }
    } else {
      setCurrentInput(prev => prev + value);
    }
  };

  const processPayment = async () => {
    if (cart.length === 0) return;

    // Check if user can create sales
    if (!canAccess.createSale(user.role)) {
      alert('You do not have permission to process sales');
      return;
    }

    try {
      // Calculate totals
      const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
      const discountAmount = (subtotal * globalDiscount) / 100;
      const taxRate = 0.1; // 10% tax
      const taxAmount = (subtotal - discountAmount) * taxRate;
      const total = subtotal - discountAmount + taxAmount;

      // Validate cash payment
      if (paymentMethod === 'cash' && cashReceived < total) {
        alert('Insufficient cash received');
        return;
      }

      // Create sale object
      const sale = {
        id: `SALE-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        items: cart.map(item => ({
          product: item.product,
          quantity: item.quantity,
          discount: 0,
          subtotal: item.total
        })),
        subtotal,
        discount: discountAmount,
        tax: taxAmount,
        total,
        paymentMethod,
        cashReceived: paymentMethod === 'cash' ? cashReceived : undefined,
        change: paymentMethod === 'cash' ? Math.max(0, cashReceived - total) : undefined,
        timestamp: new Date().toISOString(),
        cashier: user.name,
        receiptNumber: `RCP-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${Date.now().toString().slice(-6)}`
      };

      // Store sale using data sync service (offline-first)
      dataSyncService.storeSaleLocally(sale);

      // Update pending sales count
      const syncStatus = dataSyncService.getSyncStatus();
      setPendingSales(syncStatus.pendingSales);

      // Clear cart and reset form
      setCart([]);
      setGlobalDiscount(0);
      setCashReceived(0);
      setCurrentInput('');

      // Show success message
      alert(`Sale completed successfully!\nReceipt: ${sale.receiptNumber}\nTotal: $${total.toFixed(2)}`);

      setSyncStatus('✅ Sale saved and syncing to ERP...');

    } catch (error) {
      console.error('Payment error:', error);
      alert('Payment failed. Please try again.');
    } finally {
      setTimeout(() => setSyncStatus(''), 3000);
    }
  };

  const getProductCategory = (product: Product): string => {
    return product.group?.name || product.category || 'UNCATEGORIZED';
  };

  const filteredProducts = selectedCategory === 'ALL' 
    ? products 
    : products.filter(product => getProductCategory(product) === selectedCategory);

  const cartSubtotal = cart.reduce((sum, item) => sum + item.total, 0);
  const discountAmount = (cartSubtotal * globalDiscount) / 100;
  const taxRate = 0.1; // 10% tax
  const taxAmount = (cartSubtotal - discountAmount) * taxRate;
  const cartTotal = cartSubtotal - discountAmount + taxAmount;
  // Change calculation available for cash payments

  const formatCurrency = (amount: number): string => {
    return `$${amount.toFixed(2)}`;
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const handleViewSaleDetails = (sale: Sale) => {
    setSelectedSale(sale);
  };

  const handleBackToSalesList = () => {
    setSelectedSale(null);
  };

  const handleToggleViewMode = () => {
    setViewMode(viewMode === 'pos' ? 'sales' : 'pos');
    setSelectedSale(null);
  };

  const updateCartItemQuantity = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(index);
      return;
    }
    
    const updatedCart = [...cart];
    updatedCart[index].quantity = newQuantity;
    updatedCart[index].total = newQuantity * getProductPrice(updatedCart[index].product);
    setCart(updatedCart);
  };

  const removeFromCart = (index: number) => {
    const updatedCart = cart.filter((_, i) => i !== index);
    setCart(updatedCart);
  };

  const clearCart = () => {
    setCart([]);
  };

  return (
    <div className="pos-container">
      {/* Header */}
      <div className="pos-header">
        <div className="pos-header-content">
          <div className="pos-header-left">
            <div className="pos-brand">
              <div className="pos-brand-icon">
                <span className="pos-brand-icon-text">POS</span>
              </div>
              <span className="pos-brand-text">Point of Sale</span>
            </div>
            <div className="pos-user-info">
              Welcome, <span className="pos-user-name">{user?.name}</span>
            </div>
          </div>
          <div className="pos-header-right">
            {syncStatus && (
              <span className="pos-notification">
                {syncStatus}
              </span>
            )}
            <button
              onClick={handleToggleViewMode}
              className="pos-view-toggle-button"
            >
              {viewMode === 'pos' ? '📊 View Sales' : '🛒 Back to POS'}
            </button>
            <button
              onClick={syncProducts}
              disabled={isSyncing}
              className="pos-sync-button"
            >
              {isSyncing ? (
                <>
                  <div className="pos-sync-spinner"></div>
                  Syncing...
                </>
              ) : (
                '🔄 Sync Products'
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Main POS Layout */}
      <div className="pos-main-layout">
        {viewMode === 'sales' ? (
          <div className="sales-view">
            <h2 className="text-2xl font-bold">Today's Sales Summary</h2>
            {isLoadingSales ? (
              <div>Loading sales data...</div>
            ) : (
              <div>
                <div>Total Sales: {salesSummary?.total_sales || 0}</div>
                <div>Total Amount: {formatCurrency(salesSummary?.total_amount || 0)}</div>
                <div>Average Sale: {formatCurrency(salesSummary?.average_sale || 0)}</div>
                <h3 className="text-xl font-bold">Older Sales</h3>
                <ul>
                  {olderSales.map(sale => (
                    <li key={sale.id} onClick={() => handleViewSaleDetails(sale)}>
                      {sale.sale_number} - {formatCurrency(sale.total_amount)} - {formatDate(sale.sale_date)}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            <button onClick={handleBackToSalesList}>Back to Sales List</button>
          </div>
        ) : (
          <>
            {/* Cart Section */}
            <div className="pos-cart-section">
              <div className="pos-cart-header">
                <div className="pos-cart-title">Order</div>
                <div className="pos-cart-count">{cart.length}</div>
              </div>

              <div className="pos-cart-items">
                {cart.map((item, index) => (
                  <div key={index} className="pos-cart-item">
                    <div className="pos-cart-item-info">
                      <div className="pos-cart-item-name">{item.product.name}</div>
                      <div className="pos-cart-item-price">${getProductPrice(item.product).toFixed(2)} each</div>
                    </div>
                    <div className="pos-cart-item-controls">
                      <div className="pos-cart-controls">
                        <button
                          className="pos-cart-btn"
                          onClick={() => updateCartItemQuantity(index, item.quantity - 1)}
                        >
                          -
                        </button>
                        <div className="pos-cart-item-quantity">{item.quantity}</div>
                        <button
                          className="pos-cart-btn"
                          onClick={() => updateCartItemQuantity(index, item.quantity + 1)}
                        >
                          +
                        </button>
                      </div>
                      <div className="pos-cart-item-total">${item.total.toFixed(2)}</div>
                      <button
                        className="pos-cart-btn remove"
                        onClick={() => removeFromCart(index)}
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}
                {cart.length === 0 && (
                  <div style={{ textAlign: 'center', color: '#94a3b8', padding: '40px 20px' }}>
                    No items in cart
                  </div>
                )}
              </div>

              {cart.length > 0 && (
                <div className="pos-cart-summary">
                  <div className="pos-cart-subtotal">
                    <div className="pos-cart-subtotal-label">Subtotal:</div>
                    <div className="pos-cart-subtotal-amount">${cartSubtotal.toFixed(2)}</div>
                  </div>
                  <div className="pos-cart-subtotal">
                    <div className="pos-cart-subtotal-label">Tax:</div>
                    <div className="pos-cart-subtotal-amount">${taxAmount.toFixed(2)}</div>
                  </div>
                  <div className="pos-cart-subtotal">
                    <div className="pos-cart-subtotal-label">Total:</div>
                    <div className="pos-cart-subtotal-amount">${cartTotal.toFixed(2)}</div>
                  </div>
                  <div className="pos-cart-actions">
                    <button
                      className="pos-cart-action-btn pos-cart-clear-btn"
                      onClick={clearCart}
                    >
                      Clear
                    </button>
                    <button
                      className="pos-cart-action-btn pos-cart-checkout-btn"
                      onClick={processPayment}
                      disabled={cart.length === 0}
                    >
                      Checkout
                    </button>
                  </div>
                </div>
              )}

              {/* Function Buttons */}
              <div className="pos-function-buttons">
                <div className="pos-function-grid">
                  <button 
                    className="pos-function-btn pos-function-btn-primary"
                    onClick={processPayment}
                    disabled={cart.length === 0}
                  >
                    PAY
                  </button>
                  <button 
                    className="pos-function-btn pos-function-btn-danger"
                    onClick={clearCart}
                    disabled={cart.length === 0}
                  >
                    VOID
                  </button>
                  <button 
                    className="pos-function-btn pos-function-btn-warning"
                    onClick={onShowEndOfDay}
                  >
                    END OF DAY
                  </button>
                  <button 
                    className="pos-function-btn pos-function-btn-secondary"
                    onClick={() => {/* Split functionality */}}
                    disabled={cart.length === 0}
                  >
                    SPLIT
                  </button>
                  <button 
                    className="pos-function-btn pos-function-btn-info"
                    onClick={onLogout}
                  >
                    LOGOUT
                  </button>
                </div>
              </div>
            </div>

            {/* Products Section */}
            <div className="pos-products-section">
              <div className="pos-section-title">Products</div>
              
              {/* Number Input Display */}
              <div className="pos-number-display">
                <div className="pos-number-input">
                  {currentInput || '0'}
                </div>
              </div>

              {/* Category Filter */}
              <div className="pos-categories">
                <button
                  className={`pos-category-btn ${selectedCategory === 'ALL' ? 'active' : ''}`}
                  onClick={() => setSelectedCategory('ALL')}
                >
                  All
                </button>
                {categories.map((category) => (
                  <button
                    key={category}
                    className={`pos-category-btn ${selectedCategory === category ? 'active' : ''}`}
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </button>
                ))}
              </div>

              {/* Products Grid */}
              <div className="pos-products-grid">
                {isLoading ? (
                  <div style={{ textAlign: 'center', color: '#94a3b8', padding: '40px 20px' }}>
                    Loading products...
                  </div>
                ) : filteredProducts.length === 0 ? (
                  <div style={{ textAlign: 'center', color: '#94a3b8', padding: '40px 20px' }}>
                    No products found
                  </div>
                ) : (
                  filteredProducts.map((product) => (
                    <div
                      key={product.id}
                      className="pos-product-card"
                      onClick={() => handleProductClick(product)}
                    >
                      <div className="pos-product-name">{product.name}</div>
                      <div className="pos-product-price">${getProductPrice(product).toFixed(2)}</div>
                      <div className={`pos-product-stock ${
                        getProductStock(product) === 0 ? 'out' : 
                        getProductStock(product) < 10 ? 'low' : ''
                      }`}>
                        Stock: {getProductStock(product)}
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Keypad */}
              <div className="pos-keypad">
                <div className="pos-keypad-grid">
                  {['7', '8', '9', '4', '5', '6', '1', '2', '3', 'CLEAR', '0', 'REPEAT'].map((key) => (
                    <button
                      key={key}
                      onClick={() => handleKeypadClick(key)}
                      className={`pos-keypad-key ${
                        ['CLEAR', 'REPEAT'].includes(key)
                          ? 'pos-keypad-key-clear'
                          : 'pos-keypad-key-number'
                      }`}
                    >
                      {key}
                    </button>
                  ))}
                </div>
              </div>
            </div>
        </>
        )}
      </div>
     </div>
   );
 };

 export default POSInterface;
